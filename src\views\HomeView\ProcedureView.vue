<script setup lang="tsx">
import { onMounted, ref, useTemplateRef } from 'vue';
import { ElMessage } from 'element-plus';
import VirtualizedTable from '@/components/common/VirtualizedTable.vue';
import ProcedureDialog from '@/components/ProcedureView/ProcedureDialog.vue';
import { type ColumnDefinition, type RowAction } from '@/types';
import { hasPermission } from '@/script';
import { MenuPermitProcessManagement } from '@/enum';
import AdminService from '@/api/admin';
import type { MomWorkflow } from '../../../../xtrade-sdk/dist';
import { deleteConfirm } from '@/script/interaction';

// 表格引用
const tableRef = useTemplateRef('tableRef');

// 数据状态
const procedures = ref<MomWorkflow[]>([]);

// 对话框状态
const dialogVisible = ref(false);
const dialogTitle = ref('');
const currentProcedure = ref<MomWorkflow | null>(null);

// 权限检查
const canCreate = hasPermission(MenuPermitProcessManagement.创建);
const canEdit = hasPermission(MenuPermitProcessManagement.编辑);
const canDelete = hasPermission(MenuPermitProcessManagement.删除);

// 表格列定义
const columns: ColumnDefinition<MomWorkflow> = [
  {
    key: 'id',
    title: 'ID',
    width: 80,
    sortable: true,
  },
  {
    key: 'workFlowName',
    title: '流程名称',
    width: 200,
    sortable: true,
  },
  {
    key: 'content',
    title: '流程环节',
    width: 400,
    cellRenderer: ({ cellData }) => {
      const content = cellData as MomWorkflow['content'];
      return (
        <div class="flex flex-wrap gap-1">
          {content.map((item, index) => (
            <el-tag
              key={index}
              size="small"
              type={item.defaultOffLineSetting === 0 ? 'success' : 'warning'}
            >
              {item.roleName}({item.defaultOffLineSetting === 0 ? '自动通过' : '人工审核'})
            </el-tag>
          ))}
        </div>
      );
    },
  },
];

// 行操作定义
const rowActions: RowAction<MomWorkflow>[] = [
  {
    label: '编辑',
    icon: 'Edit',
    show: () => canEdit,
    onClick: row => {
      editProcedure(row);
    },
  },
  {
    label: '删除',
    icon: 'Delete',
    type: 'danger',
    show: () => canDelete,
    onClick: row => {
      deleteProcedure(row);
    },
  },
];

// 加载流程列表
const loadProcedures = async () => {
  procedures.value = await AdminService.getProcedures();
};

// 创建流程
const createProcedure = () => {
  currentProcedure.value = null;
  dialogTitle.value = '创建流程';
  dialogVisible.value = true;
};

// 编辑流程
const editProcedure = (procedure: MomWorkflow) => {
  currentProcedure.value = procedure;
  dialogTitle.value = '编辑流程';
  dialogVisible.value = true;
};

// 删除流程
const deleteProcedure = async (procedure: MomWorkflow) => {
  const result = await deleteConfirm('删除流程', `确定要删除流程"${procedure.workFlowName}"吗？`);
  if (!result) return;
  const { errorCode, errorMsg } = await AdminService.deleteProcedure(procedure.id);
  if (errorCode === 0) {
    ElMessage.success('删除成功');
    await loadProcedures();
  } else {
    ElMessage.error(errorMsg || '删除失败');
  }
};

// 保存流程
const handleSave = async () => {
  await loadProcedures();
  dialogVisible.value = false;
};

// 取消编辑
const handleCancel = () => {
  dialogVisible.value = false;
};

// 组件挂载时加载数据
onMounted(() => {
  loadProcedures();
});
</script>

<template>
  <div class="procedure-view">
    <VirtualizedTable
      ref="tableRef"
      search-placeholder="搜索流程"
      :columns="columns"
      :data="procedures"
      :row-actions="rowActions"
      :row-action-width="120"
      show-index
    >
      <template #actions>
        <div class="actions" flex aic>
          <el-button v-if="canCreate" type="primary" @click="createProcedure">
            <i class="iconfont icon-add"></i>
            <span>新建流程</span>
          </el-button>
        </div>
      </template>
    </VirtualizedTable>

    <!-- 流程设置对话框 -->
    <ProcedureDialog
      v-model:visible="dialogVisible"
      :title="dialogTitle"
      :procedure="currentProcedure"
      @save="handleSave"
      @cancel="handleCancel"
    />
  </div>
</template>

<style scoped>
.procedure-view {
  height: 100%;
  display: flex;
  flex-direction: column;
}
</style>
