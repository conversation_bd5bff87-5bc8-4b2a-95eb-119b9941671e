<div class="workflow-view-root s-border-box">
    <template>
        <div class="s-scroll-bar" style="overflow: auto">
            <div class="s-typical-toolbar">
                <el-input v-model="searchText" placeholder="搜索 流程" prefix-icon="el-icon-search" style="width: 200px; margin-right: 10px" clearable> </el-input>
                <el-button type="primary" @click="createWorkflow" size="mini"> <i class="el-icon-plus"></i> 新建流程 </el-button>
            </div>

            <!-- 流程列表 -->
            <data-tables layout="table,pagination" :data="filteredWorkflowList" :pagination-props="{ show: false, layout: 'prev,pager,next,sizes,total' }">
                <el-table-column label="NO." width="80" type="index"></el-table-column>
                <el-table-column label="流程名称" prop="workFlowName" min-width="200"></el-table-column>
                <el-table-column label="流程环节" min-width="300">
                    <template slot-scope="scope">
                        <div class="workflow-steps">
                            <el-tag v-for="(step, index) in scope.row.content" :key="index" size="mini" :type="step.defaultOffLineSetting === 0 ? 'success' : 'warning'" style="margin-right: 5px">
                                {{ step.roleName }}({{ step.defaultOffLineSetting === 0 ? '自动通过' : '人工审核' }})
                            </el-tag>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="创建时间" prop="createTime" width="150">
                    <template slot-scope="scope"> {{ formatDateTime(scope.row.createTime) }} </template>
                </el-table-column>
                <el-table-column label="操作" width="160">
                    <template slot-scope="scope">
                        <div class="opt-wrapper-5">
                            <el-button @click="editWorkflow(scope.row)" type="success" size="mini"> <i class="el-icon-edit"></i> 编辑 </el-button>
                            <el-button @click="removeWorkflow(scope.row)" type="danger" size="mini"> <i class="el-icon-close"></i> 删除 </el-button>
                        </div>
                    </template>
                </el-table-column>
            </data-tables>

            <!-- 流程设置对话框 -->
            <el-dialog width="800px" :visible="workflowDialog.visible" :close-on-click-modal="false" title="流程设置" :show-close="true" @close="cancelWorkflow">
                <el-form ref="$dlgForm" :model="workflowDialog.data" :rules="workflowDialog.rules" label-width="100px">
                    <el-form-item label="流程名字" prop="workFlowName">
                        <el-input placeholder="请设置流程名称" v-model="workflowDialog.data.workFlowName" clearable style="width: 300px"> </el-input>
                    </el-form-item>

                    <el-form-item label="流程环节">
                        <div class="workflow-steps-container">
                            <!-- 起始环节 -->
                            <div class="step-item start-step">
                                <div class="step-drag">
                                    <i class="el-icon-rank"></i>
                                </div>
                                <div class="step-name">起始</div>
                                <div class="step-role">请选择角色</div>
                                <div class="step-type">自动通过</div>
                                <div class="step-actions"></div>
                            </div>

                            <!-- 其他环节 -->
                            <div v-for="(step, index) in enrolls" :key="step.id" class="step-item">
                                <div class="step-drag">
                                    <i class="el-icon-rank"></i>
                                </div>
                                <div class="step-name">环节{{ index + 2 }}</div>
                                <div class="step-role">
                                    <el-select v-model="step.role" placeholder="请选择角色" @change="validRoleUnique(step)" style="width: 150px">
                                        <el-option v-for="(role, idx) in getAvailableRoles(index)" :label="role.label" :key="idx" :value="role.value"> </el-option>
                                    </el-select>
                                </div>
                                <div class="step-type">
                                    <el-select v-model="step.status" style="width: 120px">
                                        <el-option v-for="(status, idx) in statusEnums" :key="idx" :label="status.label" :value="status.value"> </el-option>
                                    </el-select>
                                </div>
                                <div class="step-actions">
                                    <el-button type="text" size="mini" @click="removeRole(step)" style="color: #f56c6c">
                                        <i class="el-icon-delete"></i>
                                    </el-button>
                                </div>
                            </div>

                            <!-- 添加流程按钮 -->
                            <div class="add-step-btn">
                                <el-button type="text" @click="addRole" style="color: #409eff"> <i class="el-icon-plus"></i> 添加流程 </el-button>
                            </div>
                        </div>
                    </el-form-item>
                </el-form>
                <div slot="footer">
                    <el-button @click="cancelWorkflow">取消</el-button>
                    <el-button type="primary" @click="want2saveWorkflow">确定</el-button>
                </div>
            </el-dialog>
        </div>
    </template>
</div>
