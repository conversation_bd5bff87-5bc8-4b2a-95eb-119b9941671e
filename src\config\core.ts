/**
 * 用户角色类型
 */
export const UserRole = {
  SuperAdmin: { Label: '超级管理员', Value: 1 },
  BrokerAdmin: { Label: '券商运维人员', Value: 8 },
  OrgAdmin: { Label: '机构管理员', Value: 2 },
  Counselor: { Label: '投资顾问', Value: 3 },
  ProductManager: { Label: '产品经理', Value: 4 },
  RiskProtector: { Label: '风控员', Value: 5 },
  Observer: { Label: '查看员', Value: 6 },
  TradingMan: { Label: '交易员', Value: 7 },
  InvestmentManager: { Label: '投资经理', Value: 10 },
};

/**
 * 用户登录结果
 */
export const LoginResult = {
  UnConnected: { Label: '服务器未连接', Value: -1 },
  Ok: { Label: '登录成功', Value: 0 },
  UserNameOrPasscodeError: { Label: '用户名或密码错误', Value: 1 },
  UserNameNonExist: { Label: '用户名不存在', Value: 10002 },
  PasscodeError: { Label: '密码错误', Value: 10003 },
  UserDisabled: { Label: '账号已被禁止登入', Value: 10004 },
  AlreadySignedIn: { Label: '账号已登录', Value: 10005 },
};

/**
 * 买卖方向
 */
export const TradeDirection = {
  Buy: { Label: '买', Value: 1 },
  Sell: { Label: '卖', Value: -1 },
};

/**
 * 持仓方向
 */
export const PositionDirection = {
  Long: { Label: '多头', Value: 1 },
  Short: { Label: '空头', Value: -1 },
};

/**
 * 开平仓标志
 */
export const PositionEffect = {
  Open: { Label: '开仓', Value: 0 },
  Close: { Label: '平仓', Value: 1 },
  ForceClose: { Label: '强平', Value: 2 },
  CloseToday: { Label: '平今', Value: 3 },
  CloseYesterday: { Label: '平昨', Value: 4 },
  ForceOff: { Label: '强减', Value: 5 },
  LocalForceClose: { Label: '本地强平', Value: 6 },
};

/**
 * 业务标志
 */
export enum BusinessFlagEnum {
  普通买卖 = 0,
  担保品买卖 = 1,
  融资融券 = 2,
  还款还券 = 3,
  现券还券 = 4,
  直接还款 = 5,
  新股申购 = 6,
  清算送股散股平仓 = 7,
}

/**
 * 投机套保标识
 */
export const HedgeFlag = {
  Speculation: { Label: '投机', Value: 1 },
  Arbitrage: { Label: '套利', Value: 2 },
  Hedge: { Label: '强平', Value: 3 },
};

/**
 * 委托类型
 */
export const PriceType = {
  Fix: { Label: '限价单', Value: 1 },
  Market: { Label: '市价单', Value: 2 },
  simulated: { Label: '模拟单', Value: 3 },
};

/**
 * TICK档位
 */
export const PriceLevels = [
  { Label: '最新价', Value: 11 },
  { Label: '涨停价', Value: 12 },
  { Label: '跌停价', Value: 13 },

  { Label: '对方五档', Value: 5 },
  { Label: '对方四档', Value: 4 },
  { Label: '对方三档', Value: 3 },
  { Label: '对方二档', Value: 2 },
  { Label: '对方一档', Value: 1 },
  { Label: '本方一档', Value: 6 },
  { Label: '本方二档', Value: 7 },
  { Label: '本方三档', Value: 8 },
  { Label: '本方四档', Value: 9 },
  { Label: '本方五档', Value: 10 },
];

/**
 * 资产类型
 */
export const AssetType = {
  Future: { Label: '期货', Value: 1 },
  Stock: { Label: '股票', Value: 2 },
  Option: { Label: '期权', Value: 3 },
  Bond: { Label: '债券', Value: 4 },
  PublicFund: { Label: '基金', Value: 5 },
  SpotGoods: { Label: '现货', Value: 6 },
  BuyBack: { Label: '回购', Value: 7 },
};

/** 资产类型枚举 */
export enum AssetTypeEnum {
  期货 = 1,
  股票 = 2,
  期权 = 3,
  债券 = 4,
  基金 = 5,
  现货 = 6,
  回购 = 7,
}

/** 经纪商类型枚举 */
export enum BrokerTypeEnum {
  期货 = 1,
  股票 = 2,
}

/** 日志类型枚举 */
export enum ActionLogTypeEnum {
  操作日志 = 301,
  登录日志 = 302,
}

export const TerminalType = {
  StockTerminal: { Label: '股票终端', Value: 1, AssetType: AssetTypeEnum.股票 },
  FutureTerminal: { Label: '期货终端', Value: 2, AssetType: AssetTypeEnum.期货 },
  OptionTerminal: { Label: '期权终端', Value: 3, AssetType: AssetTypeEnum.期权 },
};

/**
 * 订单状态
 */
export const OrderStatus = {
  Approving: { Label: '待审核', Value: 0 },
  Created: { Label: '订单创建', Value: 10 },
  Unreported: { Label: '订单待确认', Value: 13 },
  Reported: { Label: '订单已确认', Value: 15 },
  NotTrade: { Label: '未成交', Value: 16 },
  PartialSuccess: { Label: '部分成交', Value: 17 },
  PartialTradedCanceled: { Label: '部分成交撤单', Value: 18 },
  Succeeded: { Label: '全成', Value: 19 },
  Invalid: { Label: '废单', Value: 30 },
  ToBeCancel: { Label: '待撤', Value: 53 },
  Canceled: { Label: '已撤', Value: 54 },
  Rejected: { Label: '已驳回', Value: 55 },
};

/**
 * 账号状态
 */
export const AccountStatus = {
  Started: { Label: '新创建', Value: 0 },
  Disabled: { Label: '已下线', Value: 1 },
  Enabled: { Label: '已上线', Value: 2 },
  TradingDisabled: { Label: '禁止交易', Value: 3 },
  TradingEnabled: { Label: '允许交易', Value: 4 },
  TradingPaused: { Label: '交易暂停', Value: 5 },
  NotInitialized: { Label: '账号尚未初始化', Value: 6 },
  Discarded: { Label: '账号已废弃', Value: 7 },
};

/**
 * 审核状态
 */
export const ApprovingStatus = {
  Pending: { Label: '待审批', Value: 1 },
  Passed: { Label: '已通过', Value: 0 },
  Rejected: { Label: '已驳回', Value: 2 },
};

/**
 * 交易终端
 */
export const TradeTerminal = {
  Stock: { Label: '股票终端', Value: 1 },
  future: { Label: '期货终端', Value: 2 },
  StockOption: { Label: '股票期权终端', Value: 3 },
};

/**
 * 证券交易所
 */
export const SecurityExchange = {
  SHFE: { Label: '上期所', Value: 'SHFE' },
  DCE: { Label: '大商所', Value: 'DCE' },
  CFFEX: { Label: '中金所', Value: 'CFFEX' },
  CZCE: { Label: '郑商所', Value: 'CZCE' },
  SHSE: { Label: '上海股票交易所', Value: 'SHSE' },
  SZSE: { Label: '深圳股票交易所', Value: 'SZSE' },
  INE: { Label: '上海国际能源交易中心', Value: 'INE' },
};

/**
 * 流程类型
 */
export enum WorkflowTypeEnum {
  /** 自动通过 */
  Auto = 0,
  /** 人工审核 */
  Manual = 1,
}
