import type { EntityRiskTemplateInfo, RiskRule } from '../../../../../xtrade-sdk/dist';

export const MockedRules: RiskRule[] = [
  {
    active: true,
    beginDay: 20250904,
    beginTime: 93000,
    checkInterval: 60,
    checkObject: 3,
    configuration: {
      baseConditions: [],
      indicatorId: 5,
      kindCodes: [],
      riskParam: {},
    },
    createTime: 1756969675000,
    createUserId: 1,
    endDay: 20250904,
    endTime: 150000,
    id: 1756970999000,
    indicatorId: 5,
    orgId: 2,
    ruleName: '单笔最大交易额_250904_150755',
    templateId: 115071843893248,
    updateTime: 1756969675000,
  },
  {
    active: true,
    beginDay: 20250904,
    beginTime: 93000,
    checkInterval: 60,
    checkObject: 3,
    configuration: {
      baseConditions: [],
      indicatorId: 3,
      kindCodes: [],
      riskParam: {
        bsFlag: 1,
        paramAlert: {
          alertType: 1,
          expression: 1,
          value: 500,
        },
        paramBlock: {
          alertType: 0,
          expression: 1,
          value: 1000,
        },
      },
    },
    createTime: 1756971768000,
    createUserId: 1,
    endDay: 20250904,
    endTime: 150000,
    id: 1756971774000,
    indicatorId: 3,
    orgId: 2,
    ruleName: '单笔最大委托数量_250904_154248',
    templateId: 115071669567488,
    updateTime: 1756971768000,
  },
  {
    active: true,
    beginDay: 20250904,
    beginTime: 93000,
    checkInterval: 60,
    checkObject: 3,
    configuration: {
      baseConditions: [],
      indicatorId: 5,
      kindCodes: [],
      riskParam: {
        bsFlag: 1,
        paramAlert: {
          alertType: 1,
          expression: 1,
          value: 500,
        },
        paramBlock: {
          alertType: 0,
          expression: 1,
          value: 1000,
        },
      },
    },
    createTime: 1756969675000,
    createUserId: 1,
    endDay: 20250904,
    endTime: 150000,
    id: 1756971136000,
    indicatorId: 5,
    orgId: 2,
    ruleName: '单笔最大交易额_250904_150755',
    templateId: 115071843893248,
    updateTime: 1756969675000,
  },
];

export const MockedTemplates: EntityRiskTemplateInfo[] = [
  {
    templateId: 1001,
    templateName: '大熊市模板',
    ruleList: [MockedRules[0], MockedRules[1]],
  },
  {
    templateId: 1001,
    templateName: '牛市模板',
    ruleList: [MockedRules[2]],
  },
];
