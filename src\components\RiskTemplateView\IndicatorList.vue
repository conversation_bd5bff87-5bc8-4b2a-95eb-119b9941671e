<script setup lang="tsx">
import VirtualizedTable from '../common/VirtualizedTable.vue';
import { computed, nextTick, onMounted, ref, watch } from 'vue';
import { TableV2SortOrder, ElSwitch, ElMessage } from 'element-plus';
import type { ColumnDefinition, RowAction } from '@/types';
import { deepClone, formatDateTime, getUser, isNone, isNotJson, remove } from '@/script';
import { deleteConfirm } from '@/script/interaction';
import { RiskStepControl } from '@/enum/riskc';
import type { ContextualIndicatorInfo } from '@/types/riskc';
import { IdcComponentNameDef } from './ComponentNameDef';
import { translateCheckObject } from './IndicatorTreeNodeLogic';

import {
  Repos,
  type RiskRule,
  type RiskRuleConfiguration,
  type RiskTemplate,
} from '../../../../xtrade-sdk/dist';

interface RiskRuleRowData extends RiskRule {
  localId: number | string;
}

interface CellRenderParam {
  rowData: RiskRuleRowData;
  cellData: any;
}

// 基础列定义
const columns: ColumnDefinition<RiskRuleRowData> = [
  {
    key: 'ruleName',
    title: '风控规则名称',
    width: 250,
    minWidth: 250,
    sortable: true,
    cellRenderer: formatName,
  },
  {
    key: 'active',
    title: '是否启用',
    width: 100,
    sortable: true,
    cellRenderer: (params: CellRenderParam) => {
      const { active } = params.rowData;
      return <ElSwitch v-model={active}>{active ? '是' : '否'}</ElSwitch>;
    },
  },
  {
    key: 'beginDay',
    title: '执行日期',
    width: 200,
    minWidth: 200,
    sortable: true,
    cellRenderer: formatDayRange,
  },
  {
    key: 'beginTime',
    title: '执行时间',
    width: 150,
    minWidth: 150,
    sortable: true,
    cellRenderer: formatTimeRange,
  },
  {
    key: 'checkObject',
    title: '检查对象',
    width: 200,
    minWidth: 200,
    sortable: true,
    cellRenderer: formatCheckObject,
  },
  {
    key: 'createTime',
    title: '创建时间',
    width: 150,
    minWidth: 150,
    sortable: true,
    cellRenderer: formatDateTimeProxy,
  },
  {
    key: 'updateTime',
    title: '更新时间',
    width: 150,
    minWidth: 150,
    sortable: true,
    cellRenderer: formatDateTimeProxy,
  },
  {
    key: 'createUserId',
    title: '创建者用户ID',
    width: 120,
    sortable: true,
  },
  {
    key: 'orgId',
    title: '机构ID',
    width: 100,
    sortable: true,
  },
];

// 行操作
const rowActions: RowAction<RiskRuleRowData>[] = [
  {
    label: '删除',
    icon: 'remove',
    type: 'text',
    onClick: row => {
      deleteRow(row);
    },
  },
];

const { contextTemplate, contextIndicator } = defineProps<{
  contextTemplate?: RiskTemplate | null;
  contextIndicator?: ContextualIndicatorInfo | null;
}>();

watch(
  () => contextTemplate,
  () => {
    // 切换指标后，删除静态占位数据行
    remove(records.value, x => isNone(x.id));
  },
);

watch(
  () => contextIndicator,
  () => {
    // 切换指标后，删除静态占位数据行
    remove(records.value, x => isNone(x.id));
  },
);

const repoInstance = new Repos.RiskControlRepo();

async function deleteRow(row: RiskRuleRowData) {
  const result = await deleteConfirm('删除风控项', `确认删除此风控项： ${row.ruleName}？`);

  if (result !== true) {
    return;
  }

  if (isNone(row.id)) {
    remove(records.value, x => x === row);
    return;
  }

  const resp = await repoInstance.DeleteRule(row.id);
  const { errorCode, errorMsg } = resp;

  if (errorCode == 0) {
    remove(records.value, x => x.id == row.id);
    unselectRow();
  } else {
    ElMessage.error('风控规则删除失败：' + errorMsg);
  }
}

const user = getUser()!;
const emitter = defineEmits<{
  select: [rule: RiskRule | null];
  changed: [
    // 风控模板与其使用包含的规则数量映射关系
    tmpl2Rules: { templateId: number; ruleIds: number[] }[],
    // 风控指标与其被使用次数映射关系
    indicator2Count: { indicatorId: number; count: number }[],
  ];
}>();

function assignRuleConfig() {
  const blackListConfig: RiskRuleConfiguration = {
    indicatorId: contextIndicator?.indicatorId || (null as any),
    kindCodes: [],
    baseConditions: [],
    riskParam: null,
  };
  return blackListConfig;
}

const { instruction, entrusting, progressing, marketClosed } = RiskStepControl;

function createEmptyRule() {
  const tmpl = contextTemplate!;
  const idc = contextIndicator!;

  const date = new Date();
  const now = date.getTime();
  const today_num = Number(formatDateTime(now, 'yyyyMMdd'));
  const rname = `${idc.indicatorName}_${formatDateTime(date, 'yyMMdd')}_${formatDateTime(date, 'hhmmss')}`;

  const rule: RiskRuleRowData = {
    id: null as any,
    localId: now,
    templateId: tmpl.id,
    ruleName: rname,
    indicatorId: idc.indicatorId,
    configuration: assignRuleConfig(),
    beginTime: 93000,
    endTime: 150000,
    beginDay: today_num,
    endDay: today_num,
    checkObject: isBlackWhiteList.value
      ? instruction.value | entrusting.value | progressing.value | marketClosed.value
      : entrusting.value,
    checkInterval: 60,
    active: true,
    createUserId: user.userId,
    orgId: user.orgId,
    createTime: now,
    updateTime: now,
  };

  return rule;
}

function validateContext() {
  if (!contextTemplate) {
    return '请先选择风控模板';
  }

  if (!contextIndicator) {
    return '请先选择要风控指标';
  }

  return true;
}

function request2Add() {
  const errMsg = validateContext();
  if (typeof errMsg === 'string') {
    return ElMessage.error(errMsg);
  }

  const rule = createEmptyRule();
  records.value.unshift(rule);
  nextTick(() => {
    selectRow(rule);
  });
}

function selectRow(rule: RiskRule) {
  emitter('select', rule ? deepClone(rule) : null);
}

function unselectRow() {
  emitter('select', null);
}

const isBlackWhiteList = computed(() => {
  const cname = contextIndicator?.componentName;
  return cname == IdcComponentNameDef.BlackList || cname == IdcComponentNameDef.WhiteList;
});

const records = ref<RiskRuleRowData[]>([]);
const recordsByTmpl = computed(() => {
  const all = records.value;
  return contextTemplate ? all.filter(x => x.templateId == contextTemplate.id) : all;
});

watch(
  () => recordsByTmpl.value,
  () => {
    // 风控模版引起的过滤，进行统计上报
    reportStatistics(recordsByTmpl.value);
  },
);

const recordsByIndicator = computed(() => {
  const byTmpls = recordsByTmpl.value;
  return contextIndicator
    ? byTmpls.filter(x => x.indicatorId == contextIndicator.indicatorId)
    : byTmpls;
});

watch(
  () => recordsByIndicator.value,
  () => {
    // 风控模版一层过滤后，再由风控指标引起的二层过滤
    const filtered = recordsByIndicator.value;
    if (filtered.length > 0) {
      nextTick(() => {
        selectRow(filtered[0]);
      });
    } else if (contextIndicator?.componentName) {
      const errMsg = validateContext();
      if (typeof errMsg === 'string') {
        // 此处为静默错误，不显示
        return;
      }

      const rule = createEmptyRule();
      selectRow(rule);
    } else {
      unselectRow();
    }
  },
);

function formatDateTimeProxy(params: { cellData: any }) {
  return <span>{formatDateTime(params.cellData, 'yyyy-MM-dd hh:mm:ss')}</span>;
}

function formatName(params: { rowData: RiskRuleRowData }) {
  const { id, ruleName } = params.rowData;
  if (isNone(id)) {
    return (
      <span>
        <span class="c-red">(待保存)</span>
        <span pl-5>请在下方表单修改保存</span>
      </span>
    );
  } else {
    return <span>{ruleName}</span>;
  }
}

function formatDayRange(params: { rowData: RiskRuleRowData }) {
  const { beginDay, endDay } = params.rowData;
  const fmt = 'yyyy-MM-dd';
  return (
    <span>
      {beginDay ? formatDateTime(beginDay, fmt) : 'NA'} ~{' '}
      {endDay ? formatDateTime(endDay, fmt) : 'NA'}
    </span>
  );
}

function formatTimeRange(params: { rowData: RiskRuleRowData }) {
  function format(hms: number | string) {
    if (!hms) {
      return 'NA';
    }

    hms = hms.toString();
    while (hms.length < 6) {
      hms = '0' + hms;
    }

    const hms_str = hms.substring(0, 2) + ':' + hms.substring(2, 4) + ':' + hms.substring(4, 6);
    return hms_str;
  }

  const { beginTime, endTime } = params.rowData;
  return (
    <span>
      {format(beginTime)} ~ {format(endTime)}
    </span>
  );
}

function formatCheckObject(params: CellRenderParam) {
  const { checkObject, checkInterval } = params.rowData;
  const objects = deepClone(translateCheckObject(checkObject));
  const matched = objects.find(x => x.value == RiskStepControl.progressing.value);
  if (matched) {
    matched.label = `${matched.label} (${checkInterval}秒)`;
  }
  return <span>{objects.length == 0 ? checkObject : objects.map(x => x.label).join(', ')}</span>;
}

function reportStatistics(list: RiskRuleRowData[]) {
  // 统计风控模板与其使用包含的规则数量映射关系
  const tmpl2Rules: { templateId: number; ruleIds: number[] }[] = [];
  list.forEach(rule => {
    const { templateId } = rule;
    const matched = tmpl2Rules.find(x => x.templateId == templateId);
    if (matched) {
      matched.ruleIds.push(rule.id);
    } else {
      tmpl2Rules.push({ templateId, ruleIds: [rule.id] });
    }
  });

  // 统计风控指标与其被使用次数映射关系
  const indicator2Count: { indicatorId: number; count: number }[] = [];
  list.forEach(rule => {
    const { indicatorId } = rule;
    const matched = indicator2Count.find(x => x.indicatorId == indicatorId);
    if (matched) {
      matched.count++;
    } else {
      indicator2Count.push({ indicatorId, count: 1 });
    }
  });

  emitter('changed', tmpl2Rules, indicator2Count);
}

async function request() {
  const list = ((await repoInstance.QueryRules()).data || []) as RiskRuleRowData[];
  list.forEach(item => {
    item.localId = item.id;
  });

  // 兼容旧数据
  list.forEach(item => {
    const cfg = item.configuration;
    if (isNotJson(cfg) || cfg == null || cfg == undefined || JSON.stringify(cfg) == '{}') {
      item.configuration = assignRuleConfig();
    }
  });

  records.value = list;
  reportStatistics(list);
}

function refresh() {
  request();
}

defineExpose({
  refresh,
});

onMounted(() => {
  request();
});
</script>

<template>
  <VirtualizedTable
    ref="tableRef"
    identity="localId"
    :sort="{ key: 'createTime', order: TableV2SortOrder.DESC }"
    :columns="columns"
    :data="recordsByIndicator"
    :row-actions="rowActions"
    :row-action-width="80"
    @row-click="selectRow"
    fixed
  >
    <template #actions>
      <div class="actions" flex aic>
        <el-button type="primary" @click="request2Add">
          <span flex aic gap-6>
            <i class="iconfont icon-add"></i>
            <span>新增</span>
          </span>
        </el-button>
      </div>
    </template>
  </VirtualizedTable>
</template>

<style scoped></style>
