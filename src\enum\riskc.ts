import { enumToArray } from '@/script';

/**
 * 触发风控阈值比较符
 */
export enum RiskTriggerComparer {
  '>' = 1,
  '<' = 2,
}

/**
 * 触发风控阈值比较符
 */
export const RISK_TRIGGER_COMPARERS = enumToArray(RiskTriggerComparer);

/**
 * 风控预警类型常量
 */
export const AlertType = {
  Ignore: { label: '忽略', value: 0 },
  Warning: { label: '预警', value: 1 },
  Prevention: { label: '禁止下单', value: 2 },
  ForbiddenOfCancel: { label: '禁止撤单', value: 3 },
};

/**
 * 触发风控采取动作
 */
export const AlertTypes = Object.values(AlertType);

/**
 * 风控表达式类型常量
 */
export const ExpressionType = {
  GreaterThan: { label: '大于', value: 1 },
  GreaterEqual: { label: '大于等于', value: 2 },
  Equal: { label: '等于', value: 3 },
  LessThan: { label: '小于', value: 4 },
  LessEqual: { label: '小于等于', value: 5 },
};

/**
 * 触发风控采取动作
 */
export const ExpressionTypes = Object.values(ExpressionType);

/**
 * 风控环节控制
 */
export const RiskStepControl = {
  instruction: { label: '指令', value: 1 },
  entrusting: { label: '委托', value: 2 },
  progressing: { label: '事中', value: 4 },
  marketClosed: { label: '盘后', value: 8 },
};

/**
 * 风控环节控制
 */
export const RiskStepControls = Object.values(RiskStepControl);

/**
 * 价格偏离类型
 */
export const PriceDeviationType = {
  LatestPrice: { label: '限制与最新价偏离', value: 1 },
  YesterdayClose: { label: '限制与昨收盘价偏离', value: 2 },
};

/**
 * 价格偏离类型
 */
export const PriceDeviationTypes = Object.values(PriceDeviationType);

/**
 * 风控管控交易方向
 */
export const RiskBsFlag = {
  Buy: { label: '买入', value: 1 },
  Sell: { label: '卖出', value: 2 },
  Unset: { label: '任意方向', value: 3 },
};

/**
 * 风控管控交易方向
 */
export const RiskBsFlags = Object.values(RiskBsFlag);

/**
 * 风控价格类型
 */
export const RiskPriceType = {
  MarketPrice: { label: '限制市价', value: 1 },
  CeilingPrice: { label: '限制涨停价', value: 2 },
  FloorPrice: { label: '限制跌停价', value: 4 },
};

/**
 * 风控价格类型
 */
export const RiskPriceTypes = Object.values(RiskPriceType);

/**
 * 风控统计类型
 */
export const RiskStatisticsType = {
  SingleInstrument: { label: '单票', value: 1 },
  Summary: { label: '汇总', value: 2 },
};

/**
 * 风控统计类型
 */
export const RiskStatisticsTypes = Object.values(RiskStatisticsType);
