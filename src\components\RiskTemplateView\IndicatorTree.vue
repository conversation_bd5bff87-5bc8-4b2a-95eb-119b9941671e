<script lang="ts" setup>
import { onMounted, ref, watch } from 'vue';
import type { TreeInstance } from 'element-plus';
import { ElTree } from 'element-plus';
import { Search } from '@element-plus/icons-vue';
import { Repos, type RiskIndicator } from '../../../../xtrade-sdk/dist';
import type { ContextualIndicatorInfo } from '@/types/riskc';

import {
  buildTree,
  filterNode,
  isLeafNodeAndNoComponent,
  type LeafNode,
  type Level1Node,
} from './IndicatorTreeNodeLogic';

const repoInstance = new Repos.RiskControlRepo();
// 根据设计图生成树形数据
const treeNodes = ref<Level1Node[]>([]);
const nodeList = ref<RiskIndicator[]>([]);
const $treeRef = ref<TreeInstance>();

async function request() {
  const indicators = (await repoInstance.QueryIndicators()).data || [];
  treeNodes.value = buildTree(indicators);
  nodeList.value = indicators;
  const id2Names = indicators.map(x => ({
    indicatorId: x.id,
    indicatorName: x.indicatorName,
    componentName: x.clientName,
  }));
  emitter('report', id2Names);
}

const emitter = defineEmits<{
  report: [indicators: ContextualIndicatorInfo[]];
  select: [item: RiskIndicator];
}>();

const filterText = ref('');

watch(
  () => filterText.value,
  value => {
    $treeRef.value!.filter(value);
  },
);

function handleClick(item: LeafNode) {
  emitter('select', item.indicator);
}

function getSelcteds() {
  return ($treeRef.value!.getCheckedNodes(true, false) as LeafNode[]).map(x => x.indicator);
}

function getCurrent() {
  const node = $treeRef.value!.getCurrentNode() as LeafNode;
  return node?.indicator || null;
}

const indicatorsCountMap = ref<Record<number, number>>({});

function setIndicatorsCount(statistics: { indicatorId: number; count: number }[]) {
  const countMap: Record<number | string, number> = {};
  statistics.forEach(item => {
    // 具体叶子节点的指标统计数量
    countMap[item.indicatorId] = item.count;
    const matched_node = nodeList.value.find(x => x.id === item.indicatorId);
    if (matched_node) {
      // 父级节点的指标统计数量
      const { firstLevelCode: fcode, secondLevelCode: scode } = matched_node;
      countMap[fcode] = (countMap[fcode] || 0) + 1;
      countMap[scode] = (countMap[scode] || 0) + 1;
    }
  });

  // 父级节点的指标统计数量

  indicatorsCountMap.value = countMap;
}

onMounted(() => {
  request();
});

defineExpose({
  getCurrent,
  getSelcteds,
  setIndicatorsCount,
});
</script>

<template>
  <div class="tree-control" p-10>
    <!-- 搜索框 -->
    <el-input v-model.trim="filterText" placeholder="搜索指标" :suffix-icon="Search" clearable />
    <!-- 树形结构 -->
    <el-tree
      ref="$treeRef"
      empty-text="无指标数据"
      node-key="id"
      :props="{ label: 'name', children: 'children' }"
      :data="treeNodes"
      :filter-node-method="filterNode"
      :show-checkbox="false"
      @node-click="handleClick"
      highlight-current
      default-expand-all
    >
      <template #default="{ data }">
        <span>
          <template v-if="isLeafNodeAndNoComponent(data)">
            <span c-red>[未开发]</span>
          </template>
          <span>{{ data.name }}</span>
          <template v-if="indicatorsCountMap[data.id] !== undefined">
            <span>({{ indicatorsCountMap[data.id] }})</span>
          </template>
        </span>
      </template>
    </el-tree>
  </div>
</template>

<style scoped>
.tree-control {
  :deep() {
    .el-tree-node__label {
      font-size: 14px;
      font-weight: 400;
      color: var(--g-text-color-2);
    }

    .el-tree-node__content:hover {
      background-color: var(--g-block-bg-6);
    }

    .el-tree-node {
      &.is-current {
        > .el-tree-node__content {
          background-color: var(--g-block-bg-6) !important;
        }
      }
    }
  }
}
</style>
