import { ActionLogTypeEnum, BrokerTypeEnum, WorkflowTypeEnum } from '../../config/core';

/**
 * 菜单表
 */
export interface MomMenu {
  /** 菜单ID */
  id: number;

  /** 数据权限类型 */
  userType: number;

  /** 父类菜单ID */
  parentMenuId?: number;

  /** 菜单图标*/
  menuIcon?: string;

  /** 菜单路由(vue文件名称) */
  menuRoute?: string;

  /** 菜单名 */
  menuName: string;

  /** 排序号 */
  sequence: number;

  /** 是否激活 */
  active: boolean;

  /** 创建时间 */
  createTime?: number;

  /** 更新时间 */
  updateTime: number;

  /** 菜单权限控制项（JSON格式） */
  permissionItems: any;

  /** 备注 */
  remark?: string;
}

/** 菜单树 */
export interface MomMenuTree extends MomMenu {
  children?: MomMenuTree[];
  menListPermission?: MomPermission[];
}

/**
 * 角色菜单映射表
 */
export interface MomRoleMenuMapping {
  /** 映射ID */
  id: number;

  /** 角色ID */
  roleId: number;

  /** 角色名称 */
  roleName: string;

  /** 菜单ID */
  menuId: number;

  /** 菜单名称 */
  menuName: string;

  /** 排序号 */
  sequence: number;

  /** 创建时间 */
  createTime?: number;

  /** 更新时间 */
  updateTime: number;

  /** 菜单按钮权限配置（JSON格式） */
  permissions: any;
}

/**
 * 机构表
 */
export interface MomOrganization {
  /** 机构ID */
  id: number;

  /** 机构名 */
  orgName: string;

  /** 域名 */
  domain: string;

  /** 联系人 */
  contract: string;

  /** 电话 */
  phone: string;

  /** 邮件 */
  email: string;

  /** 状态（1启用，0禁用） */
  status: number;

  /** 机构简介 */
  introduction?: string;

  /** 创建时间 */
  createTime?: number;

  /** 更新时间 */
  updateTime: number;
}

/**
 * 数据权限类型（数值越大权限越小）
 */
export const MomUserType = {
  globalScope: { label: '全系统', value: 1 },
  orgScope: { label: '机构内', value: 2 },
  sharedScope: { label: '分享', value: 3 },
};

/**
 * 角色表
 */
export interface MomRole {
  /** 角色ID */
  id: number;

  /** 角色名称 */
  roleName: string;

  /** 数据权限类型 */
  userType: number;

  /** 机构ID（0表示系统默认） */
  orgId: number;

  /** 机构名称（默认system） */
  orgName: string;

  /** 是否启用 */
  activeFlag: boolean;

  /** 角色描述 */
  description?: string;

  /** 创建时间 */
  createTime?: number;

  /** 更新时间 */
  updateTime: number;
}

/** 创建/修改角色 */
export type FormRole = Pick<MomRole, 'roleName' | 'description'>;

/** 创建用户表单 */
export type FormUser = Partial<
  Omit<
    MomUser,
    | 'id'
    | 'createTime'
    | 'updateTime'
    | 'deleteFlag'
    | 'freezeTime'
    | 'errorNum'
    | 'firstLogin'
    | 'onlineStatus'
  >
>;

/** 创建机构表单 */
export type FormOrganization = Omit<MomOrganization, 'id' | 'createTime' | 'updateTime'>;

/** 创建菜单表单 */
export type FormMenu = Pick<MomMenu, 'menuName' | 'menuIcon' | 'parentMenuId' | 'active'>;

/** 创建权限表单 */
export type FormPermission = Pick<
  MomPermission,
  'permissionName' | 'permissionZhName' | 'functionCode' | 'method' | 'url' | 'menuId' | 'type'
>;

/** 用户密码重置 */
export interface UserPasswordReset {
  /** 用户名 */
  username: string;
  /** 新密码 */
  password: string;
}

/** 用户密码修改 */
export interface UserPasswordChange {
  /** 旧密码 */
  old_password: string;
  /** 新密码 */
  new_password: string;
  /** 用户名 */
  username: string;
}

/** 权限表 */
export interface MomPermission {
  /** 权限ID */
  id: number;
  /** 权限映射 */
  permissionName: string;
  /** 权限名称 */
  permissionZhName: string;
  /** 数据权限类型 */
  userType: number;
  /** TCP功能码 */
  functionCode: number;
  /** 通信方式 */
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'TCP';
  /** URL地址（如果为HTTP通信） */
  url?: string;
  /** 类型 */
  type: string;
  /** 菜单id */
  menuId: number;
  /** 是否为默认权限 */
  defaultPermission: 0 | 1;
  /** 创建时间 */
  createTime?: number;
  /** 更新时间 */
  updateTime: number;
}
/**
 * 用户表
 */
export interface MomUser {
  /** 用户ID */
  id: number;

  /** 用户名 */
  username: string;

  /** 真实姓名 */
  fullName: string;

  /** 邮件 */
  email: string;

  /** 电话号码 */
  phoneNo?: string;

  /** 机构ID */
  orgId: number;

  /** 机构名称 */
  orgName: string;

  /** 数据权限类型（来源于其所分配的角色，变更角色时，需同步变更该字段） */
  userType: number;

  /** 角色ID */
  roleId: number;

  /** 角色名称 */
  roleName: string;

  /** 状态（2冻结，1正常，0禁止） */
  status: number;

  /** 允许的MAC地址列表 */
  mac?: string;
  /** mac描述 */
  macDesc?: string;

  /** 创建人 */
  creator?: string;

  /** 创建时间 */
  createTime?: number;

  /** 更新时间 */
  updateTime: number;

  /** 删除标识 */
  deleteFlag: boolean;

  /** 密码错误次数 */
  errorNum: number;

  /** 冻结时间 */
  freezeTime?: number;

  /** 允许的IP地址列表 */
  ip?: string;
  /** ip描述 */
  ipDesc?: string;

  /** 磁盘序列号（预留） */
  // diskNo?: number;

  /** CPU序列号（预留） */
  // cpuNo?: number;

  /** 用户配置（JSON格式） */
  // configuration?: any;

  /** 允许登录的终端数 */
  // multiLogin: number;

  /** 明文密码 */
  password?: string;

  /** 是否首次登录 */
  firstLogin: boolean;

  /** 在线状态 */
  onlineStatus: boolean;
  /** 有效期 */
  validTime?: number;
  /** 从业资格 */
  qualifications?: string;
}

/**
 * 用户登录返回结果
 */
export interface UserLoginResponse {
  /** 错误代码 */
  errorCode: number;
  /** 错误消息（本地后补） */
  errorMsg: string | null;
  /** 是否首次登录 */
  firstLogin: boolean;
  /** 真实姓名 */
  fullName: string;
  /** 机构ID */
  orgId: number;
  /** 机构名称 */
  orgName: string;
  /** 角色ID */
  roleId: number;
  /** 登录用户授权码 */
  token: string;
  /** 用户ID */
  userId: number;
  /** 数据权限类型 */
  userType: number;
  /** 用户名 */
  username: string;
}

/**
 * 扩展后的用户信息结构
 */
export interface SysUserInfo extends UserLoginResponse {
  /** 用户角色名称 */
  roleName: string;
  /** 登录密码 */
  password: string;

  /** 是否超级管理员 */
  isSuperAdmin: boolean;
  /** 是否券商运维人员 */
  isBrokerAdmin: boolean;
  /** 是否机构管理员 */
  isOrgAdmin: boolean;
  /** 是否产品经理 */
  isProductManager: boolean;
  /** 是否风控员 */
  isRiskProtector: boolean;
  /** 是否交易员 */
  isTradingMan: boolean;
  /** 是否投顾 */
  isCounselor: boolean;
  /** 是否观察员 */
  isObserver: boolean;
  /** 该角色，是否需要登录行情服务器 */
  isQuoteServerRequired: boolean;
}

/**
 * 经纪商表
 */
export interface MomBroker {
  /** 经纪商ID */
  id: number;

  /** 经纪商代码 */
  brokerId: string;

  /** 经纪商名称 */
  brokerName: string;

  /** 经纪商类型（股票/期货/股票期货） */
  brokerType: BrokerTypeEnum;

  /** 服务器地址列表: "*******:2333,*******:1111" */
  servers: string;

  /** 创建时间 */
  createTime?: number;

  /** 更新时间 */
  updateTime: number;
}

export type FormBroker = Omit<MomBroker, 'id' | 'createTime' | 'updateTime'>;

/**
 * 终端设备表
 */
export interface MomTerminal {
  /** 终端ID */
  id: number;

  /** 终端名称（有意义的命名，如pb_qmt_broker/xxcounter_broker/saodan_broker_） */
  terminalName: string;

  /** 密码 */
  pwd: string;

  /** 描述信息 */
  description?: string;

  /** 状态（1启用，0禁用） */
  status: number;

  /** 接口类型（期货/股票/期权类型的终端） */
  interfaceType: number;

  /** 创建时间 */
  createTime?: number;

  /** 更新时间 */
  updateTime: number;
  /** 账号数量 */
  terminalAccount?: number;
}

export type FormTerminal = Omit<MomTerminal, 'id' | 'createTime' | 'updateTime'>;

/** 行为日志 */
export interface MomActionLog {
  /** 行为ID，用于标识不同类型的操作 */
  actionId: number;
  /** 行为名称，描述具体的操作类型 */
  actionName: string;
  /** 日志记录ID */
  id: number;
  /** 操作者IP地址 */
  ip: string;
  /** 日志类型 */
  level: ActionLogTypeEnum;
  /** 操作系统信息，包含IP、系统类型、主机名等 */
  os: string;
  /** 更新时间戳（毫秒） */
  updateTime: number;
  /** 用户ID */
  userId: number;
  /** 用户名 */
  userName: string;
  /** JSON格式的参数信息，登录时才有：{\"mac\":\"70-B5-E8-72-2A-20,70-B5-E8-72-2A-20\"} */
  parameter?: string;
  /** JSON格式，操作结果 */
  result?: string;
}

/** 流程 */
export interface MomWorkflow {
  /** 流程ID */
  id: number;
  /** 流程名称 */
  workFlowName: string;
  /** 机构ID */
  orgId: number;
  /** 流程内容 */
  content: Array<{
    /** 类型 */
    defaultOffLineSetting: WorkflowTypeEnum;
    /** 角色名称 */
    roleName: string;
    /** 角色类型 */
    roleType: number;
  }>;
}

/** 创建流程表单 */
export type FormWorkflow = Omit<MomWorkflow, 'id'>;
