.workflow-view-root {
    .el-tabs {
        &__item {
            height: 26px;
            line-height: 26px;
        }
        &__content {
            padding: 0;
            background-color: inherit;
            border: none;
        }
    }
    .s-typical-toolbar {
        height: 30px;
        line-height: 30px;
        padding: 8px 10px 5px;
        overflow: hidden;
    }

    .opt-wrapper-5 {
        padding: 5px 0;
    }

    .opt-wrapper {
        padding: 10px 0;
    }

    .workflow-steps {
        display: flex;
        flex-wrap: wrap;
        gap: 5px;
    }

    .workflow-steps-container {
        border: 1px solid #dcdfe6;
        border-radius: 4px;
        padding: 16px;
        background-color: #fafafa;

        .step-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px;
            margin-bottom: 12px;
            background-color: white;
            border: 1px solid #e4e7ed;
            border-radius: 4px;

            &.start-step {
                background-color: #f0f9ff;
                border-color: #409eff;
            }

            .step-drag {
                cursor: move;
                color: #909399;
                font-size: 16px;
                width: 20px;
                text-align: center;
            }

            .step-name {
                font-weight: 500;
                color: #303133;
                min-width: 60px;
            }

            .step-role {
                flex: 1;
                min-width: 150px;
                color: #606266;
            }

            .step-type {
                min-width: 120px;
                color: #606266;
            }

            .step-actions {
                width: 40px;
                text-align: center;
            }
        }

        .add-step-btn {
            text-align: center;
            padding: 12px;
            border: 2px dashed #dcdfe6;
            border-radius: 4px;
            background-color: #fafafa;

            &:hover {
                border-color: #409eff;
                background-color: #f0f9ff;
            }
        }
    }
}
