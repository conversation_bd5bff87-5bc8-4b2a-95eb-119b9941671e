<template>
  <div class="product-tutorial">
    <el-tabs v-model="activeTab" class="typical-tabs">
      <el-tab-pane :name="tabs.product">
        <template #label>
          <slot name="label">
            <span>
              <i class="iconfont icon-consist"></i>
              <span pl-8>产品信息</span>
            </span>
          </slot>
        </template>
        <ProductBasicInfoForm
          v-model="contextProduct"
          @save="basicSaved"
          @cancel="cancelEditBasic"
        ></ProductBasicInfoForm>
      </el-tab-pane>
      <el-tab-pane v-if="hasPermission(MenuPermitProductManagement.账号配置)" :name="tabs.account">
        <template #label>
          <slot name="label">
            <span>
              <i class="iconfont icon-document-code"></i>
              <span pl-8>账号管理</span>
            </span>
          </slot>
        </template>
        <AccountBinding v-model="contextProduct" @bind="bindAccounts" @unbind="unbindAccounts" />
      </el-tab-pane>
      <el-tab-pane v-if="hasPermission(MenuPermitProductManagement.人员配置)" :name="tabs.user">
        <template #label>
          <slot name="label">
            <span>
              <i class="iconfont icon-user"></i>
              <span pl-8>人员/流程设置</span>
            </span>
          </slot>
        </template>
        <ProductWorkflowSetting></ProductWorkflowSetting>
      </el-tab-pane>
      <el-tab-pane v-if="hasPermission(MenuPermitProductManagement.风控配置)" :name="tabs.risk">
        <template #label>
          <slot name="label">
            <span>
              <i class="iconfont icon-bell"></i>
              <span pl-8>风控设置</span>
            </span>
          </slot>
        </template>
        <RuleOverview></RuleOverview>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import ProductBasicInfoForm from './ProductBasicInfoForm.vue';
import AccountBinding from './AccountBinding.vue';
import ProductWorkflowSetting from './ProductWorkflowSetting.vue';
import RuleOverview from '../RiskTemplateView/EntityEntrance/RuleOverview.vue';
import type { ProductInfo } from '@/types';
import { type LegacyAccountInfo } from '../../../../xtrade-sdk/dist';
import { hasPermission, remove } from '@/script';
import { MenuPermitProductManagement } from '@/enum';

const contextProduct = defineModel<ProductInfo | null>();
const tabs = { product: 'product', account: 'account', user: 'user', risk: 'risk' };
const activeTab = ref(tabs.product);

const emitter = defineEmits<{
  cancel: [];
  save: [data: ProductInfo];
}>();

function basicSaved(row: ProductInfo) {
  emitter('save', row);
}

function cancelEditBasic() {
  emitter('cancel');
}

function unbindAccounts(accounts: LegacyAccountInfo[]) {
  const list = contextProduct.value!.accounts;
  remove(list, x => accounts.some(y => y.id == x.accountId));
  saveAccounts('unbind');
}

function bindAccounts(accounts: LegacyAccountInfo[]) {
  const list = contextProduct.value!.accounts;
  const comers = accounts.filter(x => !list.some(y => x.id == y.accountId));
  list.push(
    ...comers.map(x => {
      return {
        accountId: x.id,
        accountName: x.accountName,
        assetType: x.assetType,
        financeAccountName: x.financeAccount,
      };
    }),
  );
  saveAccounts('bind');
}

async function saveAccounts(behavior: 'bind' | 'unbind') {
  console.log(behavior);
  // 接口方法不存在报错，暂时注释掉

  // const row = contextProduct.value!;
  // const list = contextProduct.value!.accounts;
  // const { errorCode, errorMsg } = await repoGovInstance.BindProductAccounts(
  //   row.id,
  //   list.map(x => x.accountId as string),
  // );
  // if (errorCode === 0) {
  //   if (behavior == 'bind') {
  //     ElMessage.success('账号绑定成功');
  //   } else {
  //     ElMessage.success('账号解绑成功');
  //   }
  // } else {
  //   ElMessage.error(errorMsg || '账号操作失败');
  // }
}
</script>

<style scoped>
.product-tutorial {
  :deep() {
    .product-guide-slide {
      height: 600px;
    }
  }
}
</style>
